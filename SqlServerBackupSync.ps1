# 备份文件同步脚本
# 此脚本用于检查本地备份文件夹和远程备份文件夹的差异，并将缺失的文件复制到远程主机

# 配置参数
$BaseBackupPath = "D:\SQLBackups\"  # 本地备份根目录

# 仅从系统环境变量获取远程计算机名称，如果不存在则使用默认值
$RemoteComputer = [Environment]::GetEnvironmentVariable("SQL_BACKUP_REMOTE_COMPUTER", [EnvironmentVariableTarget]::Machine)
if ([string]::IsNullOrEmpty($RemoteComputer)) {
    $RemoteComputer = "desktop-a" # 如果系统环境变量不存在，使用默认值
    Write-Output "未找到系统环境变量 SQL_BACKUP_REMOTE_COMPUTER，使用默认值: $RemoteComputer"
}
Write-Output "使用备份目标计算机: $RemoteComputer"

# 添加网络备份位置配置
$RemoteSharePath = "\\$RemoteComputer\SQLBackups"  # 确保在目标计算机上已共享此文件夹

# 日志文件
$LogFile = "D:\SQLBackupLogs\Sync_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"

# 测试网络连接和共享访问的函数
function Test-NetworkShare {
    param (
        [string]$SharePath
    )

    try {
        if (Test-Path $SharePath) {
            Write-Output "网络共享路径可访问: $SharePath"
            return $true
        }
        else {
            Write-Output "无法访问网络共享路径: $SharePath"
            return $false
        }
    }
    catch {
        Write-Output "测试网络共享时出错: $_"
        return $false
    }
}

# 复制备份文件到网络位置的函数
function Copy-BackupToNetwork {
    param (
        [string]$SourcePath,
        [string]$DestinationPath
    )

    try {
        # 创建目标文件夹（如果不存在）
        if (-not (Test-Path $DestinationPath)) {
            New-Item -ItemType Directory -Path $DestinationPath -Force
            Write-Output "创建网络备份目录: $DestinationPath"
        }

        # 获取源文件名
        $fileName = Split-Path -Path $SourcePath -Leaf
        $destinationFilePath = Join-Path -Path $DestinationPath -ChildPath $fileName

        # 检查目标位置是否已存在该文件
        if (Test-Path $destinationFilePath) {
            Write-Output "远程主机上已存在文件: $destinationFilePath，跳过复制"
            return $true
        }
        else {
            # 复制文件
            Copy-Item -Path $SourcePath -Destination $DestinationPath -Force
            Write-Output "成功复制备份到网络位置: $destinationFilePath"
            return $true
        }
    }
    catch {
        Write-Output "复制备份到网络位置失败: $_"
        return $false
    }
}

# 同步备份文件夹的函数
function Sync-BackupFolders {
    param (
        [string]$LocalPath,
        [string]$RemotePath
    )

    # 检查本地路径是否存在
    if (-not (Test-Path $LocalPath)) {
        Write-Output "本地路径不存在: $LocalPath"
        return
    }

    # 确保远程路径存在
    if (-not (Test-Path $RemotePath)) {
        New-Item -ItemType Directory -Path $RemotePath -Force
        Write-Output "创建远程路径: $RemotePath"
    }

    # 获取本地文件夹中的所有文件
    $localFiles = Get-ChildItem -Path $LocalPath -File -Recurse

    # 计数器
    $totalFiles = $localFiles.Count
    $copiedFiles = 0
    $skippedFiles = 0
    $failedFiles = 0

    Write-Output "开始同步 $totalFiles 个文件..."

    # 遍历每个本地文件
    foreach ($file in $localFiles) {
        # 获取相对路径
        $relativePath = $file.FullName.Substring($LocalPath.Length)
        
        # 构建远程文件路径
        $remoteFilePath = Join-Path -Path $RemotePath -ChildPath $relativePath
        $remoteFileDir = Split-Path -Path $remoteFilePath -Parent

        # 检查远程文件是否存在
        if (Test-Path $remoteFilePath) {
            Write-Output "远程文件已存在，跳过: $remoteFilePath"
            $skippedFiles++
            continue
        }

        # 确保远程目录存在
        if (-not (Test-Path $remoteFileDir)) {
            New-Item -ItemType Directory -Path $remoteFileDir -Force | Out-Null
            Write-Output "创建远程目录: $remoteFileDir"
        }

        # 复制文件
        try {
            Copy-Item -Path $file.FullName -Destination $remoteFilePath -Force
            Write-Output "成功复制文件: $($file.FullName) -> $remoteFilePath"
            $copiedFiles++
        }
        catch {
            Write-Output "复制文件失败: $($file.FullName) -> $remoteFilePath, 错误: $_"
            $failedFiles++
        }
    }

    # 输出统计信息
    Write-Output "同步完成。总文件数: $totalFiles, 已复制: $copiedFiles, 已跳过: $skippedFiles, 失败: $failedFiles"
}

# 创建必要目录
if (-not (Test-Path (Split-Path $LogFile))) { New-Item -ItemType Directory -Path (Split-Path $LogFile) -Force }

# 开始记录日志
Start-Transcript -Path $LogFile -Append
Write-Output "备份同步开始于: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"

try {
    # 验证网络共享是否可访问
    if (-not (Test-NetworkShare -SharePath $RemoteSharePath)) {
        throw "无法访问网络备份位置，无法继续同步操作"
    }

    # 同步备份文件夹
    Write-Output "开始同步备份文件夹..."
    Sync-BackupFolders -LocalPath $BaseBackupPath -RemotePath $RemoteSharePath

    Write-Output "备份同步操作成功完成"
}
catch {
    Write-Output "错误发生: $_"
    $errorMessage = $_.Exception.Message
    Write-Output "错误详情: $errorMessage"
}
finally {
    Stop-Transcript
}

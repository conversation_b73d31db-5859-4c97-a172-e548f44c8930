# 配置参数
$SQLInstance = "localhost\SQLEXPRESS"
$DatabaseName = "ZR"
$LogFile = "D:\SQLBackupLogs\ZR_Restore_$(Get-Date -Format 'yyyyMMdd').log"

# 仅从系统环境变量获取远程计算机名称，如果不存在则使用默认值
$RemoteComputer = [Environment]::GetEnvironmentVariable("SQL_BACKUP_REMOTE_COMPUTER", [EnvironmentVariableTarget]::Machine)
if ([string]::IsNullOrEmpty($RemoteComputer)) {
  $RemoteComputer = "desktop-a" # 如果系统环境变量不存在，使用默认值
  Write-Output "未找到系统环境变量 SQL_BACKUP_REMOTE_COMPUTER，使用默认值: $RemoteComputer"
}
Write-Output "使用备份目标计算机: $RemoteComputer"

# 添加网络备份位置配置
$RemoteSharePath = "\\$RemoteComputer\SQLBackups"

# 输入验证函数
function Get-ValidatedChoice {
  param(
    [string]$Prompt,
    [int]$MinValue,
    [int]$MaxValue,
    [string[]]$AllowedSpecialValues = @()
  )

  do {
    $userInput = Read-Host $Prompt

    # 检查是否为特殊允许值（如 -1）
    if ($AllowedSpecialValues -contains $userInput) {
      return $userInput
    }

    # 尝试转换为整数并验证范围
    $choice = 0
    if ([int]::TryParse($userInput, [ref]$choice)) {
      if ($choice -ge $MinValue -and $choice -le $MaxValue) {
        return $choice
      }
    }

    $validOptions = "$MinValue 到 $MaxValue"
    if ($AllowedSpecialValues.Count -gt 0) {
      $validOptions += " 或 " + ($AllowedSpecialValues -join ", ")
    }
    Write-Output "请输入有效选择: $validOptions"
  } while ($true)
}

# 网络连接验证函数
function Test-NetworkPath {
  param([string]$Path)

  try {
    if (Test-Path $Path) {
      Write-Output "网络路径可访问: $Path"
      return $true
    }
    else {
      Write-Output "无法访问网络路径: $Path"
      return $false
    }
  }
  catch {
    Write-Output "测试网络路径时出错: $_"
    return $false
  }
}

# 数据库状态恢复函数
function Restore-DatabaseToMultiUser {
  param(
    [string]$ConnectionString,
    [string]$DatabaseName
  )

  try {
    $recoveryQuery = "ALTER DATABASE [$DatabaseName] SET MULTI_USER"
    Invoke-Sqlcmd -ConnectionString $ConnectionString -Query $recoveryQuery -ErrorAction Stop
    Write-Output "数据库已恢复到多用户模式"
    return $true
  }
  catch {
    Write-Output "无法将数据库恢复到多用户模式: $_"
    return $false
  }
}

# 创建必要目录
if (-not (Test-Path (Split-Path $LogFile))) {
  New-Item -ItemType Directory -Path (Split-Path $LogFile) -Force
}

# 开始记录日志
Start-Transcript -Path $LogFile -Append
Write-Output "恢复操作开始于: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"

try {
  # 验证网络路径可访问性
  if (-not (Test-NetworkPath -Path $RemoteSharePath)) {
    throw "无法访问网络备份路径: $RemoteSharePath"
  }

  # 加载SQL Server模块
  try {
    Import-Module "C:\Users\<USER>\Documents\PowerShell\Modules\SqlServer" -ErrorAction Stop
  }
  catch {
    Import-Module SQLPS -DisableNameChecking -ErrorAction Stop
  }

  # 连接字符串 - 连接到 master 数据库
  $connectionString = "Server=$SQLInstance;Database=master;Integrated Security=True;TrustServerCertificate=True"

  # 验证SQL Server连接
  try {
    $testQuery = "SELECT @@VERSION"
    $version = Invoke-Sqlcmd -ConnectionString $connectionString -Query $testQuery
    Write-Output "已连接到SQL Server: $($SQLInstance)"
  }
  catch {
    throw "无法连接到SQL Server实例: $SQLInstance. 错误: $_"
  }

  # 获取可用的备份周文件夹
  $weekFolders = Get-ChildItem -Path $RemoteSharePath -Directory | Sort-Object Name -Descending
  if ($weekFolders.Count -eq 0) {
    throw "未找到任何备份文件夹"
  }

  Write-Output "`n可用的备份周文件夹:"
  for ($i = 0; $i -lt $weekFolders.Count; $i++) {
    Write-Output "[$i] $($weekFolders[$i].Name)"
  }

  # 用户选择周文件夹（使用验证函数）
  $weekChoice = Get-ValidatedChoice -Prompt "`n请选择要恢复的备份周文件夹编号" -MinValue 0 -MaxValue ($weekFolders.Count - 1)
  $selectedWeekFolder = $weekFolders[$weekChoice]

  # 获取完整备份文件
  $fullBackup = Get-ChildItem -Path $selectedWeekFolder.FullName -Filter "*_Full_*.bak" | Sort-Object CreationTime -Descending | Select-Object -First 1
  if ($null -eq $fullBackup) {
    throw "在选定的周文件夹中未找到完整备份文件"
  }

  # 获取日文件夹
  $dayFolders = Get-ChildItem -Path $selectedWeekFolder.FullName -Directory | Sort-Object Name
  Write-Output "`n可用的日备份文件夹:"
  for ($i = 0; $i -lt $dayFolders.Count; $i++) {
    Write-Output "[$i] $($dayFolders[$i].Name)"
  }
  Write-Output "[-1] 仅恢复完整备份"

  # 用户选择恢复点（日文件夹）- 使用验证函数
  $maxDayIndex = if ($dayFolders.Count -gt 0) { $dayFolders.Count - 1 } else { -1 }
  $dayChoice = Get-ValidatedChoice -Prompt "`n请选择要恢复到哪一天的备份" -MinValue 0 -MaxValue $maxDayIndex -AllowedSpecialValues @("-1")

  # 准备恢复数据库
  Write-Output "`n准备恢复数据库..."

  # 确保没有活动连接
  $killConnectionsQuery = @"
ALTER DATABASE [$DatabaseName] SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
"@
  try {
    Invoke-Sqlcmd -ConnectionString $connectionString -Query $killConnectionsQuery
    Write-Output "已断开所有现有连接"
  }
  catch {
    Write-Output "断开连接时出错（数据库可能不存在）: $_"
  }

  # 开始恢复过程
  # 1. 恢复完整备份
  Write-Output "`n正在恢复完整备份: $($fullBackup.Name)"
  $restoreFullQuery = @"
RESTORE DATABASE [$DatabaseName]
FROM DISK = N'$($fullBackup.FullName)'
WITH NORECOVERY, STATS = 10
"@
  Invoke-Sqlcmd -ConnectionString $connectionString -Query $restoreFullQuery -QueryTimeout 0

  # 如果选择了特定的日期
  if ($dayChoice -ne "-1") {
    # 由于使用了验证函数，这里不需要额外的验证
    $selectedDayFolder = $dayFolders[[int]$dayChoice]

    # 2. 恢复差异备份（如果存在）
    $diffBackup = Get-ChildItem -Path $selectedDayFolder.FullName -Filter "*_Diff_*.bak" | Sort-Object CreationTime -Descending | Select-Object -First 1
    if ($null -ne $diffBackup) {
      Write-Output "`n正在恢复差异备份: $($diffBackup.Name)"
      $restoreDiffQuery = @"
RESTORE DATABASE [$DatabaseName]
FROM DISK = N'$($diffBackup.FullName)'
WITH NORECOVERY, STATS = 10
"@
      Invoke-Sqlcmd -ConnectionString $connectionString -Query $restoreDiffQuery -QueryTimeout 0
    }

    # 3. 恢复日志备份
    $logBackups = Get-ChildItem -Path $selectedDayFolder.FullName -Filter "*_Log_*.trn" | Sort-Object CreationTime
    foreach ($logBackup in $logBackups) {
      Write-Output "`n正在恢复日志备份: $($logBackup.Name)"
      $restoreLogQuery = @"
RESTORE LOG [$DatabaseName]
FROM DISK = N'$($logBackup.FullName)'
WITH NORECOVERY, STATS = 10
"@
      Invoke-Sqlcmd -ConnectionString $connectionString -Query $restoreLogQuery -QueryTimeout 0
    }
  }

  # 最终恢复步骤
  $finalRecoveryQuery = @"
RESTORE DATABASE [$DatabaseName] WITH RECOVERY;
ALTER DATABASE [$DatabaseName] SET MULTI_USER;
"@
  Invoke-Sqlcmd -ConnectionString $connectionString -Query $finalRecoveryQuery
  Write-Output "`n数据库恢复完成"

  # 验证数据库状态 - 使用参数化查询
  $verifyQuery = "SELECT name, state_desc FROM sys.databases WHERE name = @DatabaseName"
  try {
    $dbState = Invoke-Sqlcmd -ConnectionString $connectionString -Query $verifyQuery -Variable "DatabaseName='$DatabaseName'"
    Write-Output "数据库状态: $($dbState.state_desc)"
  }
  catch {
    Write-Output "验证数据库状态时出错: $_"
  }
}
catch {
  Write-Output "错误发生: $_"
  $errorMessage = $_.Exception.Message
  Write-Output "错误详情: $errorMessage"

  # 尝试将数据库恢复到多用户模式（如果可能）
  if ($connectionString) {
    Write-Output "尝试将数据库恢复到多用户模式..."
    $recovered = Restore-DatabaseToMultiUser -ConnectionString $connectionString -DatabaseName $DatabaseName
    if ($recovered) {
      Write-Output "数据库已成功恢复到多用户模式"
    }
    else {
      Write-Output "警告: 数据库可能仍处于单用户模式，请手动检查"
    }
  }
}
finally {
  Stop-Transcript
}
